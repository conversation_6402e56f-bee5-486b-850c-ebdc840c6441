/* All Visitors Details Page Styles */
.all-visitors-container {
  min-height: 100vh;
  position: relative;
  padding: 20px;
}



/* Header */
.all-visitors-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.all-visitors-header h1 {
  color: #333;
  font-size: 2.5rem;
  font-weight: 900;
  text-shadow: none;
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 0;
}

.back-button {
  background: #007bff;
  border: 2px solid #007bff;
  color: #fff;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-2px);
}

.geo-loading {
  color: #666;
  font-size: 0.9rem;
  opacity: 0.8;
  animation: pulse 2s infinite;
}

/* Controls */
.visitors-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  color: #333;
  font-weight: 600;
  font-size: 0.9rem;
}

.control-group select {
  background: #fff;
  border: 2px solid #ddd;
  color: #333;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
}

.control-group select option {
  background: #fff;
  color: #333;
}

/* Summary Stats */
.visitors-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.summary-stat {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  padding: 15px 20px;
  border-radius: 12px;
  color: #333;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Visitors Grid */
.all-visitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.visitor-card {
  background: #fff;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.visitor-card:hover {
  transform: translateY(-5px);
  background: #f8f9fa;
  border-color: #007bff;
  box-shadow: 0 10px 30px rgba(0, 123, 255, 0.2);
}

/* Visitor Card Header */
.visitor-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.visitor-location {
  display: flex;
  align-items: center;
  gap: 12px;
}

.country-flag {
  font-size: 2rem;
}

.location-info {
  display: flex;
  flex-direction: column;
}

.country-name {
  color: #333;
  font-weight: 700;
  font-size: 1.1rem;
}

.city-name {
  color: #666;
  font-size: 0.9rem;
}

.visitor-ip {
  color: #495057;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
}

/* Visitor Stats */
.visitor-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #333;
  font-size: 0.9rem;
}

.stat-icon {
  color: #666;
}

/* Visitor Details */
.visitor-details {
  margin-bottom: 15px;
}

.most-visited {
  color: #333;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.visit-times {
  display: flex;
  justify-content: space-between;
  color: #666;
  font-size: 0.8rem;
}

/* Visitor Card Footer */
.visitor-card-footer {
  color: #666;
  font-size: 0.8rem;
  text-align: center;
  padding-top: 10px;
  border-top: 1px solid #e9ecef;
}

/* Loading and Error States */
.all-visitors-loading,
.all-visitors-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  color: #333;
  font-size: 1.2rem;
  text-align: center;
}

.no-visitors {
  text-align: center;
  color: #666;
  font-size: 1.1rem;
  padding: 40px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .all-visitors-container {
    padding: 15px;
  }
  
  .all-visitors-header h1 {
    font-size: 2rem;
  }
  
  .all-visitors-grid {
    grid-template-columns: 1fr;
  }
  
  .visitors-controls {
    flex-direction: column;
  }
  
  .visitors-summary {
    flex-direction: column;
  }
  
  .visitor-card-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .visitor-stats {
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .all-visitors-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .visitor-stats {
    flex-direction: column;
    gap: 8px;
  }
}
